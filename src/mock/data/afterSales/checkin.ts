// src/mock/data/afterSales/checkin.ts

import type { 
  CheckinItem, 
  CheckinSearchParams, 
  CheckinPageResponse, 
  VehicleInfoResponse,
  VehicleSearchType,
  CheckinFormData,
  CreateRepairOrderResponse
} from '@/types/afterSales/checkin';

// 动态生成Mock数据
const generateCheckinData = (): CheckinItem[] => {
  const data: CheckinItem[] = [];
  const statuses: ('normal' | 'cancelled')[] = ['normal', 'cancelled'];
  const colors = ['白色', '黑色', '红色', '蓝色', '银色'];
  const models = ['Proton X50', 'Proton X70', 'Proton Saga', 'Proton Persona'];
  const configurations = ['标准版', '豪华版', '旗舰版']; 
  const serviceAdvisors = ['顾问1', '顾问2', '顾问3', '顾问4', '顾问5'];
  const repairPersonNames = ['客户1', '客户2', '客户3', '客户4', '客户5'];
  
  for (let i = 1; i <= 28; i++) {
    const hasRepairOrder = Math.random() > 0.7;
    const status = hasRepairOrder ? 'normal' : statuses[Math.floor(Math.random() * statuses.length)];
    
    data.push({
      id: i,
      checkinId: `DH${new Date().toISOString().slice(0, 10).replace(/-/g, '')}${String(i).padStart(3, '0')}`,
      licensePlate: `${['京', '沪', '粤', '浙'][Math.floor(Math.random() * 4)]}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(Math.random() * 90000) + 10000}`,
      vin: `WVWZZZ${Math.random().toString(36).substring(2, 15).toUpperCase()}`,
      vehicleModel: models[Math.floor(Math.random() * models.length)],
      vehicleConfiguration: configurations[Math.floor(Math.random() * configurations.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      mileage: Math.floor(Math.random() * 100000) + 1000,
      vehicleAge: Math.floor(Math.random() * 60) + 1, // 1-60个月
      repairPersonName: repairPersonNames[Math.floor(Math.random() * repairPersonNames.length)],
      repairPersonPhone: `138${Math.floor(Math.random() * 90000000) + 10000000}`,
      serviceAdvisor: serviceAdvisors[Math.floor(Math.random() * serviceAdvisors.length)],
      relatedRepairOrderId: hasRepairOrder ? `RO${Date.now()}${i}` : null,
      serviceType: 'repair',
      status,
      notes: Math.random() > 0.5 ? `备注信息${i}` : '',
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  
  return data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};

// 模拟数据
const mockData = generateCheckinData();

// 登记列表查询Mock
export const getCheckinList = (params: CheckinSearchParams): Promise<CheckinPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockData];
      
      // 搜索过滤逻辑
      if (params.checkinId) {
        filteredData = filteredData.filter(item => 
          item.checkinId.toLowerCase().includes(params.checkinId!.toLowerCase())
        );
      }
      
      if (params.licensePlate) {
        filteredData = filteredData.filter(item => 
          item.licensePlate.includes(params.licensePlate!)
        );
      }
      
      if (params.repairPersonName) {
        filteredData = filteredData.filter(item => 
          item.repairPersonName.includes(params.repairPersonName!)
        );
      }
      
      if (params.repairPersonPhone) {
        filteredData = filteredData.filter(item => 
          item.repairPersonPhone.includes(params.repairPersonPhone!)
        );
      }
      
      if (params.status && params.status !== 'all') {
        filteredData = filteredData.filter(item => item.status === params.status);
      }
      
      // 日期范围过滤
      if (params.createdAtStart) {
        filteredData = filteredData.filter(item => 
          item.createdAt >= params.createdAtStart!
        );
      }
      
      if (params.createdAtEnd) {
        filteredData = filteredData.filter(item => 
          item.createdAt <= params.createdAtEnd!
        );
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const total = filteredData.length;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        records: filteredData.slice(start, end),
        total,
        size: pageSize,
        current: page,
        pages: Math.ceil(total / pageSize)
      });
    }, 500);
  });
};

// 查询登记详情Mock
export const getCheckinDetail = (id: number): Promise<CheckinItem> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const item = mockData.find(item => item.id === id);
      if (item) {
        resolve(item);
      } else {
        reject(new Error('登记记录不存在'));
      }
    }, 300);
  });
};

// 车辆信息查询Mock
export const getVehicleInfo = (searchKey: string, searchType: VehicleSearchType): Promise<VehicleInfoResponse> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟50%的查询成功率
      if (Math.random() > 0.5) {
        resolve({
          licensePlate: searchType === 'licensePlate' ? searchKey : `京A${Math.floor(Math.random() * 90000) + 10000}`,
          vin: `WVWZZZ${Math.random().toString(36).substring(2, 15).toUpperCase()}`,
          vehicleModel: 'Proton X50',
          vehicleConfiguration: '豪华版',
          color: '白色',
          vehicleAge: Math.floor(Math.random() * 60) + 1
        });
      } else {
        reject(new Error('未查询到车辆信息'));
      }
    }, 800);
  });
};

// 新增登记Mock
export const createCheckin = (data: CheckinFormData): Promise<{ id: number; checkinId: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = mockData.length + 1;
      const checkinId = `DH${new Date().toISOString().slice(0, 10).replace(/-/g, '')}${String(newId).padStart(3, '0')}`;
      
      const newItem: CheckinItem = {
        id: newId,
        checkinId,
        licensePlate: data.licensePlate,
        vin: data.vin || '',
        vehicleModel: data.vehicleModel || '',
        vehicleConfiguration: data.vehicleConfiguration || '',
        color: data.color || '',
        mileage: data.mileage || 0,
        vehicleAge: data.vehicleAge || 0,
        repairPersonName: data.repairPersonName,
        repairPersonPhone: data.repairPersonPhone,
        serviceAdvisor: data.serviceAdvisor,
        relatedRepairOrderId: null,
        serviceType: data.serviceType,
        status: 'normal',
        notes: data.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      mockData.unshift(newItem);
      resolve({ id: newId, checkinId });
    }, 800);
  });
};

// 编辑登记Mock
export const updateCheckin = (id: number, data: Partial<CheckinFormData>): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockData.findIndex(item => item.id === id);
      if (index > -1) {
        mockData[index] = {
          ...mockData[index],
          ...data,
          updatedAt: new Date().toISOString()
        };
        resolve();
      } else {
        reject(new Error('登记记录不存在'));
      }
    }, 800);
  });
};

// 取消登记Mock
export const cancelCheckin = (id: number, reason: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockData.findIndex(item => item.id === id);
      if (index > -1) {
        mockData[index] = {
          ...mockData[index],
          status: 'cancelled',
          notes: mockData[index].notes ? `${mockData[index].notes}; 取消原因: ${reason}` : `取消原因: ${reason}`,
          updatedAt: new Date().toISOString()
        };
        resolve();
      } else {
        reject(new Error('登记记录不存在'));
      }
    }, 800);
  });
};

// 创建环检单Mock
export const createRepairOrder = (checkinId: number): Promise<CreateRepairOrderResponse> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const index = mockData.findIndex(item => item.id === checkinId);
      if (index > -1) {
        const repairOrderCode = `RO${Date.now()}${checkinId}`;
        mockData[index] = {
          ...mockData[index],
          relatedRepairOrderId: repairOrderCode,
          updatedAt: new Date().toISOString()
        };
        resolve({
          repairOrderId: Date.now(),
          repairOrderCode
        });
      } else {
        reject(new Error('登记记录不存在'));
      }
    }, 1000);
  });
};
