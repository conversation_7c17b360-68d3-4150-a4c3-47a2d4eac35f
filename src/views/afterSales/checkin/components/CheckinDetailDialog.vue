<template>
  <el-dialog
    v-model="dialogVisible"
:title="t('dialog.detailTitle')"
    width="500px"
  >
    <el-descriptions
      v-if="recordData"
      :column="1"
      border
    >
      <el-descriptions-item :label="t('checkinId')">
        {{ recordData.checkinId }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('licensePlate')">
        {{ recordData.licensePlate }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('vin')">
        {{ recordData.vin }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('vehicleModel')">
        {{ recordData.vehicleModel }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('vehicleConfiguration')">
        {{ recordData.vehicleConfiguration }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('color')">
        {{ recordData.color }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('mileage')">
        {{ recordData.mileage ? `${recordData.mileage} ${t('mileageUnit')}` : '-' }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('vehicleAge')">
        {{ recordData.vehicleAge ? `${recordData.vehicleAge} ${t('vehicleAgeUnit')}` : '-' }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('repairPersonName')">
        {{ recordData.repairPersonName }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('repairPersonPhone')">
        {{ recordData.repairPersonPhone }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('serviceAdvisor')">
        {{ recordData.serviceAdvisor }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('relatedRepairOrderId')">
        {{ recordData.relatedRepairOrderId || '-' }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('serviceType')">
        {{ t('serviceTypeOptions.repair') }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('status')">
        <el-tag 
          :type="recordData.status === 'normal' ? 'success' : 'danger'"
          size="small"
        >
          {{ recordData.status === 'normal' ? t('status.normal') : t('status.cancelled') }}
        </el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('createdAt')">
        {{ formatDateTime(recordData.createdAt) }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('updatedAt')">
        {{ formatDateTime(recordData.updatedAt) }}
      </el-descriptions-item>
      
      <el-descriptions-item :label="t('notes')">
        {{ recordData.notes || '-' }}
      </el-descriptions-item>
    </el-descriptions>

    <template #footer>
      <el-button @click="handleClose">{{ t('actions.close') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinItem } from '@/types/afterSales/checkin';

// 国际化
const { t } = useModuleI18n('afterSales.checkin');

interface Props {
  visible: boolean;
  recordData: CheckinItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-');
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  width: 120px;
}
</style>