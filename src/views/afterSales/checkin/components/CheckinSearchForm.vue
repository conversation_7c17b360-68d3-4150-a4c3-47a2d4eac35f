<template>
  <el-card class="mb-20 search-card">
    <el-form
      :model="formData"
      label-position="top"
      class="search-form"
    >
      <el-row :gutter="20">
        <!-- 第一行: 4个搜索字段 -->
        <el-col :span="6">
          <el-form-item :label="t('checkinId')">
            <el-input
              v-model="formData.checkinId"
              :placeholder="t('checkinIdPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('licensePlate')">
            <el-input
              v-model="formData.licensePlate"
              :placeholder="t('licensePlatePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('repairPersonName')">
            <el-input
              v-model="formData.repairPersonName"
              :placeholder="t('repairPersonNamePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('repairPersonPhone')">
            <el-input
              v-model="formData.repairPersonPhone"
              :placeholder="t('repairPersonPhonePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 第二行: 日期范围选择 + 状态筛选 -->
        <el-col :span="6">
          <el-form-item :label="t('createdAt')">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              clearable
              style="width: 100%"
              @change="handleDateRangeChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <!-- 第三行: 操作按钮 -->
        <el-col :span="24" class="buttons-col">
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ t('actions.search') }}
            </el-button>
            <el-button @click="handleReset">
              {{ t('actions.reset') }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinSearchParams } from '@/types/afterSales/checkin';

// 国际化
const { t } = useModuleI18n('afterSales.checkin');

interface Props {
  searchParams: CheckinSearchParams;
}

interface Emits {
  (e: 'update:searchParams', value: CheckinSearchParams): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 表单数据
const formData = reactive<CheckinSearchParams>({ ...props.searchParams });
const dateRange = ref<[string, string] | null>(null);

// 监听props变化
watch(() => props.searchParams, (newVal) => {
  Object.assign(formData, newVal);
  // 设置日期范围
  if (newVal.createdAtStart && newVal.createdAtEnd) {
    dateRange.value = [newVal.createdAtStart, newVal.createdAtEnd];
  } else {
    dateRange.value = null;
  }
}, { deep: true });

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:searchParams', { ...newVal });
}, { deep: true });

// 处理日期范围变化
const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    formData.createdAtStart = value[0];
    formData.createdAtEnd = value[1];
  } else {
    formData.createdAtStart = '';
    formData.createdAtEnd = '';
  }
};

// 搜索
const handleSearch = () => {
  emit('search');
};

// 重置
const handleReset = () => {
  Object.assign(formData, {
    checkinId: '',
    licensePlate: '',
    repairPersonName: '',
    repairPersonPhone: '',
    createdAtStart: '',
    createdAtEnd: '',
    status: ''
  });
  dateRange.value = null;
  emit('reset');
};
</script>

<style lang="scss" scoped>
.search-form {
  .el-form-item {
    margin-right: 20px;
    margin-bottom: 15px;
    &:last-child {
      margin-right: 0;
    }
  }

  .search-buttons {
    margin-bottom: 15px;
    .el-button {
      margin-left: 10px;
    }
  }
}

.buttons-col {
  text-align: right;
  :deep(.el-form-item__content) {
    justify-content: flex-end;
  }
}
</style>
