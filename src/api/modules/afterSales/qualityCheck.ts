import request from '@/api';
import type {
  QualityCheckListItem,
  QualityCheckSearchParams,
  QualityCheckDetail,
  QualityCheckSubmitForm,
  QualityCheckAuditForm,
  QualityCheckReworkForm,
  QualityCheckStatistics,
  QualityCheckTemplate,
  PaginationResponse,
  ApiResponse,
  ExportQualityCheckParams,
  BatchQualityCheckOperation
} from '@/types/afterSales/qualityCheck';
import {
  getMockQualityCheckList,
  getMockQualityCheckDetail,
  submitMockQualityCheck,
  auditMockQualityCheck,
  processMockRework,
  getMockQualityCheckStats,
  getMockQualityCheckTemplate,
  exportMockQualityCheckData,
  batchMockQualityCheckOperation
} from '@/mock/data/afterSales/qualityCheck';

// 临时使用Mock数据开关
const USE_MOCK_API_TEMP = true;

/**
 * 获取质检单列表
 */
export const getQualityCheckList = (
  params: QualityCheckSearchParams
): Promise<PaginationResponse<QualityCheckListItem>> => {
  if (USE_MOCK_API_TEMP) {
    return getMockQualityCheckList(params);
  }
  return request.get<any, PaginationResponse<QualityCheckListItem>>(
    '/after-sales/quality-check/list',
    { params }
  );
};

/**
 * 获取质检单详情
 */
export const getQualityCheckDetail = (id: string): Promise<QualityCheckDetail> => {
  if (USE_MOCK_API_TEMP) {
    return getMockQualityCheckDetail(id);
  }
  return request.get<any, QualityCheckDetail>(`/after-sales/quality-check/detail/${id}`);
};

/**
 * 提交质检结果
 */
export const submitQualityCheck = (data: QualityCheckSubmitForm): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return submitMockQualityCheck(data);
  }
  return request.post<any, ApiResponse>('/after-sales/quality-check/submit', data);
};

/**
 * 审核质检结果
 */
export const auditQualityCheck = (data: QualityCheckAuditForm): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return auditMockQualityCheck(data);
  }
  return request.post<any, ApiResponse>('/after-sales/quality-check/audit', data);
};

/**
 * 处理返工
 */
export const processRework = (data: QualityCheckReworkForm): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return processMockRework(data);
  }
  return request.post<any, ApiResponse>('/after-sales/quality-check/rework', data);
};

/**
 * 获取质检统计
 */
export const getQualityCheckStatistics = (): Promise<QualityCheckStatistics> => {
  if (USE_MOCK_API_TEMP) {
    return getMockQualityCheckStats();
  }
  return request.get<any, QualityCheckStatistics>('/after-sales/quality-check/statistics');
};

/**
 * 获取质检模板
 */
export const getQualityCheckTemplate = (workOrderType: string): Promise<QualityCheckTemplate[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockQualityCheckTemplate(workOrderType);
  }
  return request.get<any, QualityCheckTemplate[]>(`/after-sales/quality-check/template/${workOrderType}`);
};

/**
 * 导出质检数据
 */
export const exportQualityCheckData = (params: ExportQualityCheckParams): Promise<Blob> => {
  if (USE_MOCK_API_TEMP) {
    return exportMockQualityCheckData(params);
  }
  return request.get<any, Blob>(
    '/after-sales/quality-check/export',
    { 
      params,
      responseType: 'blob'
    }
  );
};

/**
 * 批量操作质检单
 */
export const batchQualityCheckOperation = (data: BatchQualityCheckOperation): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return batchMockQualityCheckOperation(data);
  }
  return request.post<any, ApiResponse>('/after-sales/quality-check/batch', data);
};

/**
 * 开始质检
 */
export const startQualityCheck = (qualityCheckId: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '质检已开始'
        });
      }, 500);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/quality-check/start/${qualityCheckId}`);
};

/**
 * 暂停质检
 */
export const pauseQualityCheck = (qualityCheckId: string, reason: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '质检已暂停'
        });
      }, 500);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/quality-check/pause/${qualityCheckId}`, { reason });
};

/**
 * 恢复质检
 */
export const resumeQualityCheck = (qualityCheckId: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '质检已恢复'
        });
      }, 500);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/quality-check/resume/${qualityCheckId}`);
};

/**
 * 取消质检
 */
export const cancelQualityCheck = (qualityCheckId: string, reason: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '质检已取消'
        });
      }, 500);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/quality-check/cancel/${qualityCheckId}`, { reason });
};

/**
 * 获取质检历史记录
 */
export const getQualityCheckHistory = (qualityCheckId: string): Promise<any[]> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            action: '创建质检单',
            operator: '系统',
            operatorId: 'system',
            operateTime: new Date().toISOString(),
            description: '质检单创建成功',
            details: {}
          },
          {
            id: '2',
            action: '开始质检',
            operator: '张师傅',
            operatorId: 'tech-001',
            operateTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            description: '技师开始进行质检',
            details: {}
          }
        ]);
      }, 300);
    });
  }
  return request.get<any, any[]>(`/after-sales/quality-check/history/${qualityCheckId}`);
};

/**
 * 保存质检草稿
 */
export const saveQualityCheckDraft = (data: QualityCheckSubmitForm): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '草稿保存成功'
        });
      }, 300);
    });
  }
  return request.post<any, ApiResponse>('/after-sales/quality-check/draft', data);
};
