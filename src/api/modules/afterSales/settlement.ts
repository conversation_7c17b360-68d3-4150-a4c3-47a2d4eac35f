import request from '@/api';
import type {
  SettlementListItem,
  SettlementSearchParams,
  SettlementDetail,
  PaymentForm,
  PaymentRecord,
  SettlementStatistics,
  PaginationResponse,
  ApiResponse,
  ExportSettlementParams,
  BatchSettlementOperation
} from '@/types/afterSales/settlement';
import {
  getMockSettlementList,
  getMockSettlementDetail,
  getMockPaymentRecords,
  processMockPayment,
  getMockSettlementStats,
  exportMockSettlementData,
  batchMockSettlementOperation
} from '@/mock/data/afterSales/settlement';

// 临时使用Mock数据开关
const USE_MOCK_API_TEMP = true;

/**
 * 获取结算单列表
 */
export const getSettlementList = (
  params: SettlementSearchParams
): Promise<PaginationResponse<SettlementListItem>> => {
  if (USE_MOCK_API_TEMP) {
    return getMockSettlementList(params);
  }
  return request.get<any, PaginationResponse<SettlementListItem>>(
    '/after-sales/settlement/list',
    { params }
  );
};

/**
 * 获取结算单详情
 */
export const getSettlementDetail = (id: string): Promise<SettlementDetail> => {
  if (USE_MOCK_API_TEMP) {
    return getMockSettlementDetail(id);
  }
  return request.get<any, SettlementDetail>(`/after-sales/settlement/detail/${id}`);
};

/**
 * 推送结算单
 */
export const pushSettlement = (id: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '结算单推送成功'
        });
      }, 1000);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/settlement/push/${id}`);
};

/**
 * 处理收退款
 */
export const processPayment = (data: PaymentForm): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return processMockPayment(data);
  }
  return request.post<any, ApiResponse>('/after-sales/settlement/payment', data);
};

/**
 * 获取收退款记录
 */
export const getPaymentRecords = (settlementId: string): Promise<PaymentRecord[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockPaymentRecords(settlementId);
  }
  return request.get<any, PaymentRecord[]>(`/after-sales/settlement/payment-records/${settlementId}`);
};

/**
 * 获取结算统计
 */
export const getSettlementStatistics = (): Promise<SettlementStatistics> => {
  if (USE_MOCK_API_TEMP) {
    return getMockSettlementStats();
  }
  return request.get<any, SettlementStatistics>('/after-sales/settlement/statistics');
};

/**
 * 导出结算数据
 */
export const exportSettlementData = (params: ExportSettlementParams): Promise<Blob> => {
  if (USE_MOCK_API_TEMP) {
    return exportMockSettlementData(params);
  }
  return request.get<any, Blob>(
    '/after-sales/settlement/export',
    { 
      params,
      responseType: 'blob'
    }
  );
};

/**
 * 批量操作结算单
 */
export const batchSettlementOperation = (data: BatchSettlementOperation): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return batchMockSettlementOperation(data);
  }
  return request.post<any, ApiResponse>('/after-sales/settlement/batch', data);
};

/**
 * 完成结算
 */
export const completeSettlement = (id: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '结算完成'
        });
      }, 800);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/settlement/complete/${id}`);
};

/**
 * 取消结算
 */
export const cancelSettlement = (id: string, reason: string): Promise<ApiResponse> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '结算已取消'
        });
      }, 800);
    });
  }
  return request.post<any, ApiResponse>(`/after-sales/settlement/cancel/${id}`, { reason });
};

/**
 * 获取结算历史记录
 */
export const getSettlementHistory = (settlementId: string): Promise<any[]> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            action: '创建结算单',
            operator: '系统',
            operatorId: 'system',
            operateTime: new Date().toISOString(),
            description: '结算单创建成功',
            details: {}
          },
          {
            id: '2',
            action: '推送结算',
            operator: '陈经理',
            operatorId: 'advisor-001',
            operateTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            description: '结算单已推送给客户',
            details: {}
          }
        ]);
      }, 300);
    });
  }
  return request.get<any, any[]>(`/after-sales/settlement/history/${settlementId}`);
};
