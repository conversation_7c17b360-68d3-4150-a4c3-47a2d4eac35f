import { createRouter, createWebHistory } from 'vue-router';
import CustomerDetailView from '@/views/CustomerDetailView.vue';
import HomeView from '@/views/HomeView.vue';
import InventoryReportView from '@/views/InventoryReportView.vue';
import InventoryReportHQView from '@/views/InventoryReportHQView.vue';
import SalesOrderView from "../views/SalesOrderView.vue";
import DashboardView from '../views/afterSales/dashboard/DashboardView.vue';
import AppointmentsView from '../views/afterSales/appointments/AppointmentsView.vue';
import QuotaView from '../views/afterSales/quota/QuotaView.vue';
import DispatchManagementView from '../views/dispatch/DispatchManagementView.vue';
import LoginView from '@/views/LoginView.vue';
import UserInfoTestView from '@/views/UserInfoTestView.vue';
import { useAuthStore } from '@/stores/auth';
import { ElMessage } from 'element-plus';
import { salesRoutes } from './modules/sales';
import financeRoutes from './modules/finance';
import { partsRoutes } from './modules/parts';
import { baseRoutes } from './modules/base';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 登录页面
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: {
        title: 'login.title',
        requiresAuth: false,
        hidden: true
      }
    },
    // 基础页面
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: 'menu.home',
        icon: 'HomeFilled',
        requiresAuth: true // 首页不需要权限控制，任何人都可以访问
      }
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('@/views/AboutView.vue'),
      meta: {
        title: 'menu.about',
        requiresAuth: true,
        hidden: true // 不在菜单中显示
      }
    },
    {
      path: '/user-info-test',
      name: 'user-info-test',
      component: UserInfoTestView,
      meta: {
        title: '用户信息测试',
        requiresAuth: true,
        hidden: true // 不在菜单中显示
      }
    },
    {
      path: '/permission-test',
      name: 'permission-test',
      component: () => import('@/views/PermissionTestView.vue'),
      meta: {
        title: '权限控制测试',
        requiresAuth: true,
        hidden: true // 不在菜单中显示
      }
    },

    // 客户管理模块
    {
      path: '/customer-detail',
      name: 'customer-detail',
      component: CustomerDetailView,
      meta: {
        title: 'menu.customerDetail',
        icon: 'User'
      }
    },
    {
      path: '/after-sales/checkin',
      name: 'CheckinManagement',
      component: () => import('@/views/afterSales/checkin/CheckinView.vue'),
      meta: {
        title: 'menu.checkinList',
        requiresAuth: false,
        icon: 'List'
      }
    },

    // 预约管理模块
    {
      path: '/after-sales/dashboard',
      name: 'AppointmentDashboard',
      component: DashboardView,
      meta: {
        title: 'menu.appointmentDashboard',
        requiresAuth: true,
        icon: 'Monitor'
      }
    },
    {
      path: '/after-sales/appointments',
      name: 'Appointments',
      component: AppointmentsView,
      meta: {
        title: 'menu.appointments',
        requiresAuth: true,
        icon: 'Calendar'
      }
    },
    {
      path: '/after-sales/quota',
      name: 'QuotaManagement',
      component: QuotaView,
      meta: {
        title: 'menu.quotaManagement',
        requiresAuth: true,
        icon: 'Setting'
      }
    },

    // 销售订单模块
    {
      path: '/sales-order',
      name: 'sales-order',
      component: SalesOrderView,
      meta: {
        title: 'menu.salesOrder',
        icon: 'Document'
      }
    },
    {
      path: '/sales-order-edit/:orderNo',
      name: 'sales-order-edit',
      component: () => import('../views/customerOrderManagement/SalesOrderEditView.vue'),
      meta: {
        title: 'menu.salesOrderEdit',
        requiresAuth: true,
        hidden: true
      }
    },
    {
      path: '/order-detail/:orderNo',
      name: 'order-detail',
      component: () => import('../views/OrderDetailView.vue'),
      meta: {
        title: 'menu.salesOrderDetail',
        hidden: true // 不在菜单中显示
      }
    },
    // {
    //   path: '/order-edit',
    //   name: 'order-edit',
    //   component: OrderEditView,
    //   meta: {
    //     title: '销售订单编辑',
    //     hidden: true // 不在菜单中显示
    //   }
    // },
    // {
    //   path: '/order-detail',
    //   name: 'order-detail',
    //   component: OrderDetailView,
    //   meta: {
    //     title: '销售订单详情',
    //     hidden: true // 不在菜单中显示
    //   }
    // },
    // {
    //   path: '/sales-order-management',
    //   name: 'sales-order-management',
    //   component: () => import('../views/customerOrderManagement/SalesOrderManagementView.vue'),
    //   meta: {
    //     title: '销售订单管理',
    //     requiresAuth: true,
    //     icon: 'Document'
    //   }
    // },

    // 库存管理模块
    {
      path: '/inventory-report',
      name: 'inventory-report',
      component: InventoryReportView,
      meta: {
        title: 'menu.inventoryReport',
        icon: 'Box'
      }
    },
    {
      path: '/inventory-report-hq',
      name: 'inventory-report-hq',
      component: InventoryReportHQView,
      meta: {
        title: 'menu.inventoryReportHQ',
        icon: 'DataAnalysis'
      }
    },

    // 零件管理模块
    {
      path: '/part-management',
      name: 'part-management',
      component: () => import('@/views/parts/management/ManagementView.vue'),
      meta: {
        title: 'menu.partManagement',
        icon: 'Tools'
      }
    },
    {
      path: '/parts-management-hq',
      name: 'parts-management-hq',
      component: () => import('@/views/PartsManagementHQView.vue'),
      meta: {
        title: 'menu.partsManagementHQ',
        requiresAuth: true,
        icon: 'Tools'
      }
    },
    {
      path: '/parts-receipt',
      name: 'parts-receipt',
      component: () => import('@/views/PartsReceiptView.vue'),
      meta: {
        title: 'menu.partsReceipt',
        requiresAuth: true,
        icon: 'Box'
      }
    },
    {
      path: '/parts-receipt-query',
      name: 'parts-receipt-query',
      component: () => import('@/views/PartsReceiptQueryView.vue'),
      meta: {
        title: 'menu.partsReceiptQuery',
        requiresAuth: true,
        icon: 'Search'
      }
    },
    {
      path: '/parts/archives',
      name: 'part-archives',
      component: () => import('@/views/parts/archives/ArchivesView.vue'),
      meta: {
        title: 'menu.partArchives',
        requiresAuth: true,
        icon: 'Files'
      }
    },
    {
      path: '/part-configuration',
      name: 'part-configuration',
      component: () => import('@/views/PartConfigurationView.vue'),
      meta: {
        title: 'menu.partConfiguration',
        requiresAuth: true,
        icon: 'Setting'
      }
    },

    // 环检单管理模块（重构版本）
    {
      path: '/after-sales/inspection',
      name: 'InspectionManagement',
      component: () => import('@/views/afterSales/inspection/InspectionView.vue'),
      meta: {
        title: 'menu.inspectionManagement',
        requiresAuth: true,
        icon: 'Document'
      }
    },
    {
      path: '/after-sales/work-assignment',
      name: 'WorkAssignmentManagement',
      component: () => import('../views/afterSales/workAssignment/WorkAssignmentView.vue'),
      meta: {
        title: 'menu.workAssignmentManagement',
        requiresAuth: true,
        icon: 'List'
      }
    },
    {
      path: '/after-sales/work-assignment-dashboard',
      name: 'WorkAssignmentDashboard',
      component: () => import('../views/afterSales/workAssignment/WorkAssignmentDashboardView.vue'),
      meta: {
        title: 'menu.workAssignmentDashboard',
        requiresAuth: true,
        icon: 'DataBoard'
      }
    },
    // 工单管理模块（重构版本）
    {
      path: '/after-sales/work-order',
      name: 'WorkOrderManagement',
      component: () => import('@/views/afterSales/workOrder/WorkOrderView.vue'),
      meta: {
        title: 'menu.workOrderManagement',
        requiresAuth: true,
        icon: 'Document'
      }
    },

    // 质检管理模块
    {
      path: '/quality-check',
      name: 'quality-check',
      component: () => import('@/views/qualityCheck/QualityCheckList.vue'),
      meta: {
        title: '质检管理',
        requiresAuth: true,
        icon: 'Check'
      }
    },

    // 派工管理模块
    {
      path: '/dispatch-management',
      name: 'dispatch-management',
      component: DispatchManagementView,
      meta: {
        title: 'menu.dispatchManagement',
        requiresAuth: true,
        icon: 'User'
      }
    },
    {
      path: '/work-assignment-dashboard',
      name: 'work-assignment-dashboard',
      component: () => import('@/views/workAssignment/WorkAssignmentDashboard.vue'),
      meta: {
        title: 'menu.workAssignmentDashboard',
        requiresAuth: true,
        icon: 'Monitor'
      }
    },

    // 审批管理模块
    {
      path: '/approval',
      name: 'approval',
      component: () => import('@/views/approval/OrderApprovalView.vue'),
      meta: {
        title: '订单审批',
        requiresAuth: true,
        icon: 'Check'
      }
    },
    {
      path: '/work-order-approval',
      name: 'work-order-approval',
      component: () => import('@/views/workorder/approval/index.vue'),
      meta: {
        title: '工单审批（旧版）',
        requiresAuth: true,
        icon: 'Check',
        hidden: true // 隐藏旧版本
      }
    },
    // 工单审批模块（重构版本）
    {
      path: '/after-sales/work-order-approval',
      name: 'WorkOrderApproval',
      component: () => import('@/views/afterSales/workOrderApproval/WorkOrderApprovalView.vue'),
      meta: {
        title: 'menu.workOrderApproval',
        requiresAuth: true,
        icon: 'DocumentChecked'
      }
    },
    // 车间派工模块（重构版本）
    {
      path: '/after-sales/dispatch',
      name: 'DispatchManagement',
      component: () => import('@/views/afterSales/dispatch/DispatchManagementView.vue'),
      meta: {
        title: 'menu.dispatchManagement',
        requiresAuth: true,
        icon: 'Operation'
      }
    },
    // 质量管理模块（重构版本）
    {
      path: '/after-sales/quality-check',
      name: 'QualityCheckManagement',
      component: () => import('@/views/afterSales/qualityCheck/QualityCheckManagementView.vue'),
      meta: {
        title: 'menu.qualityCheckManagement',
        requiresAuth: true,
        icon: 'DocumentChecked'
      }
    },
    // 结算管理模块（重构版本）
    {
      path: '/after-sales/settlement',
      name: 'SettlementManagement',
      component: () => import('@/views/afterSales/settlement/SettlementManagementView.vue'),
      meta: {
        title: 'menu.settlementManagement',
        requiresAuth: true,
        icon: 'Money'
      }
    },


    {
      path: '/vehicle-allocation',
      name: 'vehicle-allocation',
      component: () => import('@/views/customerOrderManagement/VehicleAllocationManagement.vue'),
      meta: {
        title: 'menu.vehicleAllocation',
        requiresAuth: true,
        icon: 'Truck'
      }
    },

    // 订单管理模块
    // {
    //   path: '/order-management',
    //   name: 'order-management',
    //   component: () => import('@/views/order/OrderManagementView.vue'),
    //   meta: {
    //     title: '订单管理',
    //     requiresAuth: true,
    //     icon: 'Document'
    //   }
    // },
    // 订单审批管理（重构版本）
    {
      path: '/sales/order-approval',
      name: 'OrderApproval',
      component: () => import('@/views/sales/orderApproval/OrderApprovalView.vue'),
      meta: {
        title: 'menu.orderApproval',
        requiresAuth: true,
        icon: 'DocumentChecked'
      }
    },
    // 旧版订单审批管理（保留兼容性）
    {
      path: '/order-approval-management',
      name: 'order-approval-management',
      component: () => import('../views/customerOrderManagement/OrderApprovalManagementView.vue'),
      meta: {
        title: 'menu.orderApprovalManagement',
        requiresAuth: true,
        icon: 'Check',
        hidden: true // 隐藏旧版本
      }
    },
    // 交付管理（重构版本）
    {
      path: '/sales/delivery',
      name: 'Delivery',
      component: () => import('@/views/sales/delivery/DeliveryView.vue'),
      meta: {
        title: 'menu.deliveryManagement',
        requiresAuth: true,
        icon: 'Truck'
      }
    },
    // 旧版交付管理（保留兼容性）
    {
      path: '/delivery-management',
      name: 'delivery-management',
      component: () => import('@/views/customerOrderManagement/DeliveryManagementView.vue'),
      meta: {
        title: 'menu.deliveryManagement',
        requiresAuth: true,
        icon: 'Truck',
        hidden: true // 隐藏旧版本
      }
    },
    {
      path: '/vehicle-registration',
      name: 'vehicle-registration',
      component: () => import('../views/customerOrderManagement/VehicleRegistrationView.vue'),
      meta: {
        title: 'menu.vehicleRegistration',
        requiresAuth: true,
        icon: 'Edit'
      }
    },
    // 旧版发票管理（保留兼容性）
    {
      path: '/invoice-management',
      name: 'invoice-management',
      component: () => import('../views/customerOrderManagement/InvoiceManagementView.vue'),
      meta: {
        title: 'menu.invoiceManagement',
        requiresAuth: true,
        icon: 'Tickets',
        hidden: true // 隐藏旧版本，使用新的重构版本
      }
    },

    // 厂端销售管理模块
    {
      path: '/lead-pool-management',
      name: 'lead-pool-management',
      component: () => import('../views/prospective-customer/factory-prospect/index.vue'),
      meta: {
        title: 'menu.leadPoolManagement',
        requiresAuth: true,
        icon: 'User'
      }
    },
    {
      path: '/order-statistics-management',
      name: 'order-statistics-management',
      component: () => import('../views/factorySalesManagement/OrderStatisticsManagementView.vue'),
      meta: {
        title: 'menu.orderStatisticsManagement',
        requiresAuth: true,
        icon: 'DataAnalysis'
      }
    },


    // 财务管理模块（重构版本）
    ...financeRoutes,

    // 零件管理模块（重构版本）
    ...partsRoutes,

    // 旧版整车收款管理（保留兼容性）
    {
      path: '/whole-vehicle-collection-management',
      name: 'whole-vehicle-collection-management',
      component: () => import('../views/financeManagement/WholeVehicleCollectionManagementView.vue'),
      meta: {
        title: '整车收款管理（旧版）',
        requiresAuth: true,
        icon: 'Money',
        hidden: true // 隐藏旧版本
      }
    },

    // 售后结算管理模块
    {
      path: '/settlement/list',
      name: 'settlement-management',
      component: () => import('@/views/settlement/SettlementManagementView.vue'),
      meta: {
        title: '结算管理',
        requiresAuth: true,
        icon: 'Money'
      }
    },


    // 潜客管理模块路由
    {
      path: '/sales-prospect',
      name: 'sales-prospect',
      component: () => import('@/views/prospective-customer/sales-prospect/index.vue'),
      meta: {
        title: 'menu.salesProspect',
        requiresAuth: true,
        icon: 'UserFilled'
      }
    },
    {
      path: '/factory-prospect',
      name: 'factory-prospect',
      component: () => import('@/views/prospective-customer/factory-prospect/index.vue'),
      meta: {
        title: 'menu.factoryProspect',
        icon: 'factory',
        requiresAuth: true
      }
    },
    {
      path: '/defeat-audit',
      name: 'defeat-audit',
      component: () => import('@/views/prospective-customer/defeat-audit/index.vue'),
      meta: {
        title: 'menu.defeatAudit',
        icon: 'audit',
        requiresAuth: true
      }
    },
    {
      path: '/add-prospect',
      name: 'add-prospect',
      component: () => import('@/views/prospective-customer/add-prospect/index.vue'),
      meta: {
        title: 'menu.addProspect',
        icon: 'plus',
        requiresAuth: true
      }
    },

    {
      path: '/order',
      name: 'order',
      component: () => import('../views/order/OrderManagementView.vue'),
      meta: {
        title: 'menu.order',
        requiresAuth: true,
        icon: 'Document'
      }
    },

    // 基础模块路由
    ...baseRoutes,

    // 系统管理模块
    {
      path: '/system/store',
      name: 'system-store',
      component: () => import('@/views/system/StoreManagement.vue'),
      meta: {
        title: 'permission.store.title',
        requiresAuth: true,
        icon: 'OfficeBuilding'
      }
    },
    {
      path: '/system/department',
      name: 'system-department',
      component: () => import('@/views/system/DepartmentManagement.vue'),
      meta: {
        title: 'permission.department.title',
        requiresAuth: true,
        icon: 'List'
      }
    },
    {
      path: '/system/menu',
      name: 'system-menu',
      component: () => import('@/views/system/MenuManagement.vue'),
      meta: {
        title: 'permission.menu.title',
        requiresAuth: true,
        icon: 'Menu'
      }
    },
    {
      path: '/system/role',
      name: 'system-role',
      component: () => import('@/views/system/RoleManagement.vue'),
      meta: {
        title: 'permission.role.title',
        requiresAuth: true,
        icon: 'UserFilled'
      }
    },

    {
      path: '/system/user',
      name: 'system-user',
      component: () => import('@/views/system/UserManagement.vue'),
      meta: {
        title: 'permission.user.title',
        requiresAuth: true,
        icon: 'User'
      }
    },

    // 开发测试页面
    {
      path: '/test/token',
      name: 'token-test',
      component: () => import('@/views/TokenTestView.vue'),
      meta: {
        title: 'Token传输测试',
        requiresAuth: true,
        icon: 'Key',
        hidden: import.meta.env.PROD // 生产环境隐藏
      }
    },

    // 销售模块路由
    ...salesRoutes
  ],
})

// 路由守卫：检查认证和更新页面标题
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 初始化认证状态（如果有token但没有用户信息，尝试获取用户信息）
  if (authStore.token && !authStore.userInfo) {
    try {
      await authStore.initAuth()
    } catch (error) {
      console.error('初始化认证状态失败:', error)
    }
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false && !authStore.isLoggedIn) {
    // 如果路由需要认证但用户未登录，重定向到登录页面
    ElMessage.warning('请先登录')
    next({
      name: 'login',
      query: { redirect: to.fullPath } // 保存目标路由，登录后可以重定向回来
    })
    return
  }

  // 如果已登录用户访问登录页，重定向到首页
  if (to.name === 'login' && authStore.isLoggedIn) {
    next({ name: 'home' })
    return
  }

    // 更新页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - DMS 经销商管理系统`
  }

  next()
});

export default router
